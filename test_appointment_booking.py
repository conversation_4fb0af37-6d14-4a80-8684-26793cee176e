#!/usr/bin/env python3
"""
Test script to verify appointment booking functionality
"""

import json
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.tools_health_consult import automated_health_consultation, appointment_intelligence_scheduler

def test_appointment_booking_links():
    """Test that appointment booking links are properly included in health consultation responses"""
    
    print("🧪 Testing Appointment Booking Integration...")
    print("=" * 50)
    
    # Test data with health issues that should trigger appointment recommendations
    test_health_data = {
        "user_id": "test_user_123",
        "health_data": {
            "vital_signs": {
                "data": {
                    "Blood Pressure (Systolic)": 160,  # High blood pressure
                    "Blood Pressure (Diastolic)": 95,   # High blood pressure
                    "Glucose": 140,                      # Elevated glucose
                    "SpO2": 92,                         # Low oxygen saturation
                    "ECG (Heart Rate)": 110,            # Elevated heart rate
                    "Weight (BMI)": 32,                 # Obesity
                    "Temperature": 98.6,
                    "Malaria": "Negative",
                    "Widal Test": "Negative",
                    "Hepatitis B": "Negative",
                    "Voluntary Serology": "Negative"
                },
                "result": {
                    "analysis": "Multiple vital signs outside normal ranges",
                    "alerts": "High blood pressure and elevated glucose detected"
                },
                "timestamp": "2024-01-15T10:30:00"
            },
            "health_score": {
                "result": {
                    "Total Score": 45,  # Poor health score
                    "Health Status": "Poor",
                    "Vitals Needing Improvement": ["Blood Pressure", "Glucose", "Weight"],
                    "Improvement Tips": ["Monitor blood pressure daily", "Reduce sugar intake", "Increase physical activity"]
                },
                "timestamp": "2024-01-15T10:30:00"
            }
        }
    }
    
    print("1. Testing Appointment Intelligence Scheduler...")
    print("-" * 40)
    
    # Test appointment intelligence scheduler
    scheduler_result = appointment_intelligence_scheduler(json.dumps(test_health_data))
    scheduler_data = json.loads(scheduler_result)
    
    print(f"✅ Recommended Appointments: {scheduler_data.get('recommended_appointments', [])}")
    print(f"✅ Recommended Tests: {scheduler_data.get('recommended_tests', [])}")
    
    # Check if appointment booking link is included
    if "appointment_booking_link" in scheduler_data:
        print(f"✅ Appointment Booking Link: {scheduler_data['appointment_booking_link']}")
        print(f"✅ Booking Message: {scheduler_data['booking_message']}")
        
        # Verify it's the correct TurboMedics URL
        expected_url = "https://app.turbomedics.com/patient/appointment"
        if scheduler_data['appointment_booking_link'] == expected_url:
            print("✅ Correct TurboMedics appointment URL found!")
        else:
            print(f"❌ Wrong URL! Expected: {expected_url}, Got: {scheduler_data['appointment_booking_link']}")
    else:
        print("❌ No appointment booking link found in scheduler result")
    
    print("\n2. Testing Automated Health Consultation...")
    print("-" * 40)
    
    # Test automated health consultation
    consultation_result = automated_health_consultation(json.dumps(test_health_data))
    consultation_data = json.loads(consultation_result)
    
    print(f"✅ Doctor Visit Recommended: {consultation_data.get('doctor_visit_recommended', False)}")
    print(f"✅ Urgency Level: {consultation_data.get('urgency_level', 'Unknown')}")
    print(f"✅ Medical Advice Count: {len(consultation_data.get('medical_advice', []))}")
    print(f"✅ Specialist Recommendations: {consultation_data.get('specialist_recommendations', [])}")
    
    # Check if appointment booking link is included
    if "appointment_booking_link" in consultation_data:
        print(f"✅ Appointment Booking Link: {consultation_data['appointment_booking_link']}")
        print(f"✅ Booking Message: {consultation_data['booking_message']}")
        
        # Verify it's the correct TurboMedics URL
        expected_url = "https://app.turbomedics.com/patient/appointment"
        if consultation_data['appointment_booking_link'] == expected_url:
            print("✅ Correct TurboMedics appointment URL found!")
        else:
            print(f"❌ Wrong URL! Expected: {expected_url}, Got: {consultation_data['appointment_booking_link']}")
    else:
        print("❌ No appointment booking link found in consultation result")
    
    print("\n3. Testing with Healthy Data (should not trigger appointment booking)...")
    print("-" * 40)
    
    # Test with healthy data
    healthy_data = {
        "user_id": "healthy_user_123",
        "health_data": {
            "vital_signs": {
                "data": {
                    "Blood Pressure (Systolic)": 120,  # Normal
                    "Blood Pressure (Diastolic)": 80,   # Normal
                    "Glucose": 90,                       # Normal
                    "SpO2": 98,                         # Normal
                    "ECG (Heart Rate)": 75,             # Normal
                    "Weight (BMI)": 23,                 # Normal
                    "Temperature": 98.6,
                    "Malaria": "Negative",
                    "Widal Test": "Negative",
                    "Hepatitis B": "Negative",
                    "Voluntary Serology": "Negative"
                },
                "result": {
                    "analysis": "All vital signs within normal ranges",
                    "alerts": "No abnormal patterns detected"
                },
                "timestamp": "2024-01-15T10:30:00"
            },
            "health_score": {
                "result": {
                    "Total Score": 85,  # Good health score
                    "Health Status": "Good",
                    "Vitals Needing Improvement": ["None"],
                    "Improvement Tips": ["Continue healthy lifestyle"]
                },
                "timestamp": "2024-01-15T10:30:00"
            }
        }
    }
    
    healthy_consultation = automated_health_consultation(json.dumps(healthy_data))
    healthy_consultation_data = json.loads(healthy_consultation)
    
    print(f"✅ Doctor Visit Recommended: {healthy_consultation_data.get('doctor_visit_recommended', False)}")
    print(f"✅ Urgency Level: {healthy_consultation_data.get('urgency_level', 'Unknown')}")
    
    # For healthy data, appointment booking link might still be present for routine care
    if "appointment_booking_link" in healthy_consultation_data:
        print(f"✅ Appointment Booking Link (for routine care): {healthy_consultation_data['appointment_booking_link']}")
    else:
        print("ℹ️ No appointment booking link (expected for healthy data)")
    
    print("\n" + "=" * 50)
    print("🎉 Appointment Booking Integration Test Complete!")
    print("=" * 50)

if __name__ == "__main__":
    test_appointment_booking_links()
