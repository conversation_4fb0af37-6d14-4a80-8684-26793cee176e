#!/usr/bin/env python3
"""
Test script to verify the health consultation endpoint is working
"""

import requests
import json
import sys
import os

# Test configuration
API_URL = "http://localhost:8000"  # Adjust if your server runs on a different port

def test_health_consultation_endpoint():
    """Test the health consultation endpoint"""
    
    print("🧪 Testing Health Consultation Endpoint...")
    print("=" * 60)
    
    # First, let's add some test health data for a user
    test_user_id = "test_user_123"
    
    print("1. Adding test health data...")
    print("-" * 40)
    
    # Add vital signs data
    vital_signs_data = {
        "user_id": test_user_id,
        "vital_signs": {
            "Blood Pressure (Systolic)": 160,  # High
            "Blood Pressure (Diastolic)": 95,   # High
            "Glucose": 140,                      # Elevated
            "SpO2": 92,                         # Low
            "ECG (Heart Rate)": 110,            # Elevated
            "Weight (BMI)": 32,                 # Obesity
            "Temperature": 98.6,
            "Malaria": "Negative",
            "Widal Test": "Negative",
            "Hepatitis B": "Negative",
            "Voluntary Serology": "Negative",
            "Perfusion_index": 2.5,
            "Waist Circumference": 40,
            "Fev": 3.2
        }
    }
    
    try:
        # Add vital signs
        response = requests.post(f"{API_URL}/vital-signs", json=vital_signs_data, timeout=10)
        if response.status_code == 200:
            print("✅ Vital signs data added successfully")
        else:
            print(f"⚠️ Failed to add vital signs: {response.status_code}")
            print(f"Response: {response.text}")
    except requests.RequestException as e:
        print(f"❌ Error adding vital signs: {str(e)}")
        return False
    
    # Add health score data
    health_score_data = {
        "user_id": test_user_id,
        "health_data": {
            "Blood Pressure (Systolic)": 160,
            "Blood Pressure (Diastolic)": 95,
            "Glucose": 140,
            "SpO2": 92,
            "ECG (Heart Rate)": 110,
            "Weight (BMI)": 32,
            "Temperature": 98.6,
            "Malaria": "Negative",
            "Widal Test": "Negative",
            "Hepatitis B": "Negative",
            "Voluntary Serology": "Negative"
        }
    }
    
    try:
        # Add health score
        response = requests.post(f"{API_URL}/realtime-health-score", json=health_score_data, timeout=10)
        if response.status_code == 200:
            print("✅ Health score data added successfully")
        else:
            print(f"⚠️ Failed to add health score: {response.status_code}")
    except requests.RequestException as e:
        print(f"⚠️ Error adding health score: {str(e)}")
    
    print("\n2. Testing Health Consultation Endpoint...")
    print("-" * 40)
    
    # Test the health consultation endpoint
    try:
        # Test GET method
        response = requests.get(f"{API_URL}/health-consultation", 
                              params={"user_id": test_user_id}, 
                              timeout=30)
        
        if response.status_code == 200:
            print("✅ Health consultation endpoint is working!")
            
            result = response.json()
            
            # Check the response structure
            if "consultation" in result:
                print("✅ Consultation response received")
                consultation_text = result["consultation"]
                
                # Check for key components
                if "Hi there!" in consultation_text:
                    print("✅ Friendly greeting included")
                
                if "appointment" in consultation_text.lower():
                    print("✅ Appointment booking information included")
                
                if "turbomedics.com" in consultation_text:
                    print("✅ TurboMedics appointment link included")
                
                if "Book Now" in consultation_text:
                    print("✅ TurboMedics page instructions included")
                
                if "Online Appointment" in consultation_text and "Physical Appointment" in consultation_text:
                    print("✅ Appointment type explanations included")
                
                # Check other response fields
                if "doctor_visit_recommended" in result:
                    print(f"✅ Doctor visit recommended: {result['doctor_visit_recommended']}")
                
                if "urgency_level" in result:
                    print(f"✅ Urgency level: {result['urgency_level']}")
                
                if "appointment_recommendations" in result:
                    appointments = result["appointment_recommendations"]
                    print(f"✅ Appointment recommendations: {len(appointments)} specialists")
                
                if "recommended_tests" in result:
                    tests = result["recommended_tests"]
                    print(f"✅ Recommended tests: {len(tests)} tests")
                
                if "appointment_booking_link" in result:
                    booking_link = result["appointment_booking_link"]
                    print(f"✅ Appointment booking link: {booking_link}")
                    
                    if booking_link == "https://app.turbomedics.com/patient/appointment":
                        print("✅ Correct TurboMedics URL!")
                    else:
                        print(f"❌ Wrong booking URL: {booking_link}")
                
                print("\n📋 Sample Consultation Response (first 500 characters):")
                print("-" * 50)
                print(consultation_text[:500] + "..." if len(consultation_text) > 500 else consultation_text)
                print("-" * 50)
                
            else:
                print("❌ No consultation field in response")
                print(f"Response keys: {list(result.keys())}")
        
        elif response.status_code == 404:
            print("❌ Health consultation endpoint not found (404)")
            print("The endpoint might not be properly configured")
        
        else:
            print(f"❌ Health consultation endpoint returned error: {response.status_code}")
            print(f"Response: {response.text}")
            
            # Try to parse error message
            try:
                error_data = response.json()
                if "error" in error_data:
                    print(f"Error message: {error_data['error']}")
            except:
                pass
    
    except requests.RequestException as e:
        print(f"❌ Error connecting to health consultation endpoint: {str(e)}")
        print("Make sure the server is running on the correct port")
        return False
    
    print("\n3. Testing POST method...")
    print("-" * 40)
    
    # Test POST method
    try:
        post_data = {"user_id": test_user_id}
        response = requests.post(f"{API_URL}/health-consultation", 
                               json=post_data, 
                               timeout=30)
        
        if response.status_code == 200:
            print("✅ POST method also working!")
        else:
            print(f"⚠️ POST method returned: {response.status_code}")
    
    except requests.RequestException as e:
        print(f"⚠️ Error testing POST method: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🎉 Health Consultation Endpoint Test Complete!")
    print("=" * 60)
    
    return True

def check_server_status():
    """Check if the server is running"""
    try:
        response = requests.get(f"{API_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and healthy")
            return True
        else:
            print(f"⚠️ Server responded with status: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"❌ Cannot connect to server: {str(e)}")
        print(f"Make sure the server is running at {API_URL}")
        return False

if __name__ == "__main__":
    print("🏥 Health Consultation Endpoint Test")
    print("=" * 60)
    
    # Check server status first
    if check_server_status():
        test_health_consultation_endpoint()
    else:
        print("\n❌ Cannot proceed with tests - server is not accessible")
        print("Please start the agent server first with: python agent_server.py")
