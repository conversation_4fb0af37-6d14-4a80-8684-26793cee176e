from langchain.tools import Tool
import json
from datetime import datetime, timedelta
import logging

def appointment_intelligence_scheduler(health_data_json: str) -> str:
    """
    Intelligent appointment scheduler that recommends checkups/tests based on user health history
    and provides proactive care coordination.
    """
    try:
        # Parse input data
        input_data = json.loads(health_data_json)

        # Handle both direct data and user_health_data format
        if "user_id" in input_data and "health_data" in input_data:
            user_id = input_data["user_id"]
            user_health_data = input_data["health_data"]
        else:
            user_id = "unknown"
            user_health_data = input_data

        # Initialize response structure
        recommendation_result = {
            "timestamp": datetime.now().isoformat(),
            "recommended_appointments": [],
            "recommended_tests": [],
            "follow_up_schedule": {},
            "care_coordination": [],
            "urgency_levels": {},
            "appointment_reasons": {}
        }

        # Extract all available health data
        vital_signs_data = {}
        health_score_data = {}
        kidney_function_data = {}
        lipid_profile_data = {}
        lung_capacity_data = {}
        test_results_data = {}

        # Get vital signs data
        if "vital_signs" in user_health_data:
            vital_signs_data = user_health_data["vital_signs"].get("data", {})
            vital_signs_timestamp = user_health_data["vital_signs"].get("timestamp", "")

        # Get health score data
        if "health_score" in user_health_data:
            health_score_data = user_health_data["health_score"].get("result", {})
            health_score_timestamp = user_health_data["health_score"].get("timestamp", "")

        # Get kidney function data
        if "kidney_function" in user_health_data:
            kidney_function_data = user_health_data["kidney_function"].get("result", {})
            kidney_function_timestamp = user_health_data["kidney_function"].get("timestamp", "")

        # Get lipid profile data
        if "lipid_profile" in user_health_data:
            lipid_profile_data = user_health_data["lipid_profile"].get("result", {})
            lipid_profile_timestamp = user_health_data["lipid_profile"].get("timestamp", "")

        # Get lung capacity data
        if "lung_capacity" in user_health_data:
            lung_capacity_data = user_health_data["lung_capacity"].get("result", {})
            lung_capacity_timestamp = user_health_data["lung_capacity"].get("timestamp", "")

        # Get test results data
        if "test_results" in user_health_data:
            test_results_data = user_health_data["test_results"].get("result", {})
            test_results_timestamp = user_health_data["test_results"].get("timestamp", "")

        # Analyze health data and recommend appointments/tests

        # 1. Check vital signs for abnormalities
        if vital_signs_data:
            # Check blood pressure
            systolic = vital_signs_data.get("Blood Pressure (Systolic)")
            diastolic = vital_signs_data.get("Blood Pressure (Diastolic)")

            if systolic is not None and diastolic is not None:
                if systolic > 140 or diastolic > 90:
                    recommendation_result["recommended_appointments"].append("Cardiologist")
                    recommendation_result["recommended_tests"].append("24-hour Blood Pressure Monitoring")
                    recommendation_result["urgency_levels"]["Cardiologist"] = "Medium" if systolic > 160 or diastolic > 100 else "Low"
                    recommendation_result["appointment_reasons"]["Cardiologist"] = "Elevated blood pressure requiring evaluation"
                    recommendation_result["follow_up_schedule"]["Blood Pressure Check"] = "2 weeks"
                    recommendation_result["care_coordination"].append("Regular blood pressure monitoring at home")

            # Check glucose levels
            glucose = vital_signs_data.get("Glucose")
            if glucose is not None and glucose > 126:
                recommendation_result["recommended_appointments"].append("Endocrinologist")
                recommendation_result["recommended_tests"].append("HbA1c Test")
                recommendation_result["recommended_tests"].append("Fasting Blood Glucose")
                recommendation_result["urgency_levels"]["Endocrinologist"] = "Medium" if glucose > 200 else "Low"
                recommendation_result["appointment_reasons"]["Endocrinologist"] = "Elevated blood glucose requiring evaluation"
                recommendation_result["follow_up_schedule"]["Glucose Check"] = "1 month"
                recommendation_result["care_coordination"].append("Regular glucose monitoring")

            # Check oxygen saturation
            spo2 = vital_signs_data.get("SpO2")
            if spo2 is not None and spo2 < 95:
                recommendation_result["recommended_appointments"].append("Pulmonologist")
                recommendation_result["recommended_tests"].append("Pulmonary Function Test")
                recommendation_result["urgency_levels"]["Pulmonologist"] = "High" if spo2 < 90 else "Medium"
                recommendation_result["appointment_reasons"]["Pulmonologist"] = "Low oxygen saturation requiring evaluation"
                recommendation_result["follow_up_schedule"]["Oxygen Check"] = "1 week"
                recommendation_result["care_coordination"].append("Oxygen therapy evaluation")

        # 2. Check kidney function data
        if kidney_function_data:
            kidney_stage = kidney_function_data.get("kidney_stage", "")
            overall_health = kidney_function_data.get("overall_health", "")

            if "G3" in kidney_stage or "G4" in kidney_stage or "G5" in kidney_stage or "severe" in overall_health.lower():
                recommendation_result["recommended_appointments"].append("Nephrologist")
                recommendation_result["recommended_tests"].append("Comprehensive Kidney Function Panel")
                recommendation_result["recommended_tests"].append("Renal Ultrasound")
                recommendation_result["urgency_levels"]["Nephrologist"] = "High" if "G5" in kidney_stage else "Medium"
                recommendation_result["appointment_reasons"]["Nephrologist"] = f"Kidney function concerns ({kidney_stage})"
                recommendation_result["follow_up_schedule"]["Kidney Function"] = "1 month"
                recommendation_result["care_coordination"].append("Renal diet consultation")
                recommendation_result["care_coordination"].append("Medication review for kidney-friendly options")

        # 3. Check lipid profile data
        if lipid_profile_data:
            ascvd_risk = lipid_profile_data.get("ascvd_risk", "")

            if "high" in ascvd_risk.lower() or "elevated" in ascvd_risk.lower():
                recommendation_result["recommended_appointments"].append("Cardiologist")
                recommendation_result["recommended_tests"].append("Cardiac Stress Test")
                recommendation_result["recommended_tests"].append("Echocardiogram")
                recommendation_result["urgency_levels"]["Cardiologist"] = "Medium"
                recommendation_result["appointment_reasons"]["Cardiologist"] = "Elevated cardiovascular risk"
                recommendation_result["follow_up_schedule"]["Lipid Profile"] = "3 months"
                recommendation_result["care_coordination"].append("Cardiac rehabilitation program")
                recommendation_result["care_coordination"].append("Nutritionist consultation for heart-healthy diet")

        # 4. Check lung capacity data
        if lung_capacity_data:
            respiratory_risk = lung_capacity_data.get("respiratory_risk_level", "")

            if "high" in respiratory_risk.lower() or "moderate" in respiratory_risk.lower():
                recommendation_result["recommended_appointments"].append("Pulmonologist")
                recommendation_result["recommended_tests"].append("Complete Pulmonary Function Test")
                recommendation_result["recommended_tests"].append("Chest X-ray")
                recommendation_result["urgency_levels"]["Pulmonologist"] = "Medium" if "high" in respiratory_risk.lower() else "Low"
                recommendation_result["appointment_reasons"]["Pulmonologist"] = "Reduced lung capacity requiring evaluation"
                recommendation_result["follow_up_schedule"]["Lung Function"] = "3 months"
                recommendation_result["care_coordination"].append("Respiratory therapy consultation")
                recommendation_result["care_coordination"].append("Pulmonary rehabilitation program")

        # 5. Check test results data
        if test_results_data:
            if test_results_data.get("malaria_result") == "Positive":
                recommendation_result["recommended_appointments"].append("Infectious Disease Specialist")
                recommendation_result["urgency_levels"]["Infectious Disease Specialist"] = "High"
                recommendation_result["appointment_reasons"]["Infectious Disease Specialist"] = "Positive malaria test"
                recommendation_result["follow_up_schedule"]["Malaria Test"] = "2 weeks"
                recommendation_result["care_coordination"].append("Antimalarial medication management")

            if test_results_data.get("widal_result") == "Positive":
                recommendation_result["recommended_appointments"].append("Infectious Disease Specialist")
                recommendation_result["urgency_levels"]["Infectious Disease Specialist"] = "Medium"
                recommendation_result["appointment_reasons"]["Infectious Disease Specialist"] = "Positive Widal test"
                recommendation_result["follow_up_schedule"]["Widal Test"] = "1 month"
                recommendation_result["care_coordination"].append("Antibiotic therapy monitoring")

        # 6. Check overall health score
        if health_score_data:
            total_score = health_score_data.get("Total Score", 0)
            health_status = health_score_data.get("Health Status", "Unknown")

            # Skip "Unknown" health status
            if health_status != "Unknown" and (total_score < 50 or health_status.lower() == "poor"):
                recommendation_result["recommended_appointments"].append("Primary Care Physician")
                recommendation_result["recommended_tests"].append("Comprehensive Health Panel")
                recommendation_result["urgency_levels"]["Primary Care Physician"] = "Medium"
                recommendation_result["appointment_reasons"]["Primary Care Physician"] = "Low overall health score requiring comprehensive evaluation"
                recommendation_result["follow_up_schedule"]["Health Score"] = "1 month"
                recommendation_result["care_coordination"].append("Health coach consultation")
                recommendation_result["care_coordination"].append("Lifestyle modification program")

        # 7. Add routine preventive care recommendations
        recommendation_result["recommended_tests"].append("Annual Physical Examination")
        recommendation_result["recommended_tests"].append("Complete Blood Count")
        recommendation_result["recommended_tests"].append("Lipid Profile")
        recommendation_result["follow_up_schedule"]["Routine Checkup"] = "1 year"

        # Remove duplicates
        recommendation_result["recommended_appointments"] = list(set(recommendation_result["recommended_appointments"]))
        recommendation_result["recommended_tests"] = list(set(recommendation_result["recommended_tests"]))
        recommendation_result["care_coordination"] = list(set(recommendation_result["care_coordination"]))

        # Add appointment booking link if there are recommended appointments
        if recommendation_result["recommended_appointments"]:
            recommendation_result["appointment_booking_link"] = "https://app.turbomedics.com/patient/appointment"
            recommendation_result["booking_message"] = "Click the link above to book your recommended appointments with healthcare professionals. Once you get to the TurboMedics appointment page, click on the 'Book Now' button. You'll see two appointment types: Online Appointment (virtual consultation via video call) and Physical Appointment (in-person visit at a healthcare facility)."

        return json.dumps(recommendation_result, indent=4)

    except Exception as e:
        logging.error(f"Error in appointment intelligence scheduler: {str(e)}")
        return json.dumps({
            "error": f"Failed to process appointment recommendations: {str(e)}",
            "recommended_appointments": ["Primary Care Physician"],
            "recommended_tests": ["Annual Physical Examination"],
            "follow_up_schedule": {"Routine Checkup": "1 year"},
            "care_coordination": ["General health monitoring"]
        }, indent=4)

def automated_health_consultation(health_data_json: str) -> str:
    """
    Comprehensive health consultation that analyzes all available health data
    and provides integrated medical advice, specialist recommendations, and
    appointment intelligence with proactive care coordination.
    """
    try:
        # Parse input data
        input_data = json.loads(health_data_json)

        # Handle both direct data and user_health_data format
        if "user_id" in input_data and "health_data" in input_data:
            user_id = input_data["user_id"]
            user_health_data = input_data["health_data"]
        else:
            user_id = "unknown"
            user_health_data = {"data": input_data.get("data", {})}

        # Initialize response structure
        consultation_result = {
            "timestamp": datetime.now().isoformat(),
            "medical_advice": [],
            "specialist_recommendations": [],
            "health_summary": {},
            "doctor_visit_recommended": False,
            "urgency_level": "Low",  # Low, Medium, High, Critical
            "integrated_analysis": [],
            "appointment_recommendations": [],
            "recommended_tests": [],
            "follow_up_schedule": {},
            "care_coordination": []
        }

        # Get appointment intelligence recommendations
        try:
            appointment_intelligence_result = json.loads(appointment_intelligence_scheduler(health_data_json))

            # Add appointment intelligence data to consultation result
            consultation_result["appointment_recommendations"] = appointment_intelligence_result.get("recommended_appointments", [])
            consultation_result["recommended_tests"] = appointment_intelligence_result.get("recommended_tests", [])
            consultation_result["follow_up_schedule"] = appointment_intelligence_result.get("follow_up_schedule", {})
            consultation_result["care_coordination"] = appointment_intelligence_result.get("care_coordination", [])

            # Add appointment reasons to the consultation result
            consultation_result["appointment_reasons"] = appointment_intelligence_result.get("appointment_reasons", {})

            # Add urgency levels for appointments
            consultation_result["appointment_urgency_levels"] = appointment_intelligence_result.get("urgency_levels", {})

        except Exception as e:
            logging.error(f"Error getting appointment intelligence: {str(e)}")
            # Continue with consultation even if appointment intelligence fails

        # Extract all available health data
        vital_signs_data = {}
        health_score_data = {}
        kidney_function_data = {}
        lipid_profile_data = {}

        # Get vital signs data
        if "vital_signs" in user_health_data:
            vital_signs_data = user_health_data["vital_signs"].get("data", {})
            consultation_result["health_summary"]["vital_signs"] = {
                "timestamp": user_health_data["vital_signs"].get("timestamp", ""),
                "alerts": user_health_data["vital_signs"].get("result", {}).get("alerts", "")
            }
        elif "data" in user_health_data:
            # Use direct data if available
            vital_signs_data = user_health_data["data"]

        # Get health score data
        if "health_score" in user_health_data:
            health_score_data = user_health_data["health_score"].get("result", {})
            consultation_result["health_summary"]["health_score"] = {
                "score": health_score_data.get("Total Score", 0),
                "status": health_score_data.get("Health Status", "Unknown"),
                "timestamp": user_health_data["health_score"].get("timestamp", "")
            }

        # Get kidney function data
        if "kidney_function" in user_health_data:
            kidney_function_data = user_health_data["kidney_function"].get("result", {})
            consultation_result["health_summary"]["kidney_function"] = {
                "overall_health": kidney_function_data.get("overall_health", "Unknown"),
                "confidence_level": kidney_function_data.get("confidence_level", "Unknown"),
                "kidney_stage": kidney_function_data.get("kidney_stage", "Unknown"),
                "timestamp": user_health_data["kidney_function"].get("timestamp", "")
            }

        # Get lipid profile data
        if "lipid_profile" in user_health_data:
            lipid_profile_data = user_health_data["lipid_profile"].get("result", {})
            consultation_result["health_summary"]["lipid_profile"] = {
                "ascvd_risk": lipid_profile_data.get("ascvd_risk", "Unknown"),
                "timestamp": user_health_data["lipid_profile"].get("timestamp", "")
            }

        # Analyze vital signs
        medical_advice = []
        specialist_recommendations = []
        integrated_analysis = []
        need_doctor_visit = False
        urgency_level = "Low"

        # Check glucose levels
        glucose = vital_signs_data.get("Glucose")
        if glucose == "Unknown":
            # Skip Unknown values - don't count them as issues
            pass
        elif glucose is not None and isinstance(glucose, (int, float)):
            if glucose > 200:
                medical_advice.append("🚨 Severely elevated blood sugar detected. This requires immediate medical attention.")
                specialist_recommendations.append("**Endocrinologist**: For management of potential diabetes.")
                integrated_analysis.append("Your glucose level is significantly elevated, which may indicate diabetes.")
                need_doctor_visit = True
                urgency_level = "High"
            elif glucose > 130:
                medical_advice.append("⚠️ Elevated blood sugar detected. This may indicate prediabetes or diabetes.")
                specialist_recommendations.append("**Endocrinologist**: For evaluation of glucose metabolism.")
                integrated_analysis.append("Your glucose level is higher than normal, suggesting possible prediabetic condition.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")
            elif glucose < 70:
                medical_advice.append("⚠️ Low blood sugar detected. This may cause dizziness, confusion, and weakness.")
                integrated_analysis.append("Your glucose level is below normal range, which can lead to hypoglycemic symptoms.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")

        # Check blood pressure
        systolic = vital_signs_data.get("Blood Pressure (Systolic)")
        diastolic = vital_signs_data.get("Blood Pressure (Diastolic)")

        # Skip Unknown values
        if systolic == "Unknown" or diastolic == "Unknown":
            # Skip Unknown values - don't count them as issues
            pass
        elif systolic is not None and diastolic is not None and isinstance(systolic, (int, float)) and isinstance(diastolic, (int, float)):
            if systolic > 180 or diastolic > 120:
                medical_advice.append("🚨 Hypertensive crisis detected. Seek immediate medical attention.")
                specialist_recommendations.append("**Cardiologist**: For urgent evaluation and management of severe hypertension.")
                integrated_analysis.append("Your blood pressure is at crisis level, posing immediate risk to your cardiovascular health.")
                need_doctor_visit = True
                urgency_level = "Critical"
            elif systolic > 140 or diastolic > 90:
                medical_advice.append("⚠️ Hypertension detected. This increases risk of heart disease and stroke.")
                specialist_recommendations.append("**Cardiologist**: For evaluation and management of hypertension.")
                integrated_analysis.append("Your blood pressure is elevated, which increases your risk of cardiovascular disease.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")
            elif systolic < 90 or diastolic < 60:
                medical_advice.append("⚠️ Low blood pressure detected. This may cause dizziness and fainting.")
                integrated_analysis.append("Your blood pressure is lower than normal, which may lead to inadequate blood flow to organs.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")

        # Check heart rate
        heart_rate = vital_signs_data.get("ECG (Heart Rate)")
        if heart_rate == "Unknown":
            # Skip Unknown values - don't count them as issues
            pass
        elif heart_rate is not None and isinstance(heart_rate, (int, float)):
            if heart_rate > 120:
                medical_advice.append("⚠️ Tachycardia detected. This may indicate heart problems or other conditions.")
                specialist_recommendations.append("**Cardiologist**: For evaluation of elevated heart rate.")
                integrated_analysis.append("Your heart rate is significantly elevated, which may indicate underlying cardiovascular issues.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")
            elif heart_rate < 50:
                medical_advice.append("⚠️ Bradycardia detected. This may indicate heart problems or medication effects.")
                specialist_recommendations.append("**Cardiologist**: For evaluation of low heart rate.")
                integrated_analysis.append("Your heart rate is lower than normal, which may affect cardiac output and tissue perfusion.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")

        # Check oxygen saturation
        spo2 = vital_signs_data.get("SpO2")
        if spo2 == "Unknown":
            # Skip Unknown values - don't count them as issues
            pass
        elif spo2 is not None and isinstance(spo2, (int, float)):
            if spo2 < 90:
                medical_advice.append("🚨 Severe hypoxemia detected. This requires immediate medical attention.")
                specialist_recommendations.append("**Pulmonologist**: For urgent evaluation of respiratory function.")
                integrated_analysis.append("Your oxygen saturation is dangerously low, indicating potential respiratory failure.")
                need_doctor_visit = True
                urgency_level = "Critical"
            elif spo2 < 95:
                medical_advice.append("⚠️ Mild hypoxemia detected. This may indicate respiratory problems.")
                specialist_recommendations.append("**Pulmonologist**: For evaluation of respiratory function.")
                integrated_analysis.append("Your oxygen saturation is below normal, suggesting possible respiratory insufficiency.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")

        # Check BMI
        bmi = vital_signs_data.get("Weight (BMI)")
        if bmi == "Unknown":
            # Skip Unknown values - don't count them as issues
            pass
        elif bmi is not None and isinstance(bmi, (int, float)):
            if bmi > 35:
                medical_advice.append("⚠️ Class II obesity detected. This significantly increases risk of multiple health conditions.")
                specialist_recommendations.append("**Nutritionist/Dietitian**: For weight management program.")
                specialist_recommendations.append("**Endocrinologist**: For metabolic evaluation.")
                integrated_analysis.append("Your BMI indicates significant obesity, which increases risk for diabetes, heart disease, and other conditions.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")
            elif bmi > 30:
                medical_advice.append("⚠️ Class I obesity detected. This increases risk of multiple health conditions.")
                specialist_recommendations.append("**Nutritionist/Dietitian**: For weight management guidance.")
                integrated_analysis.append("Your BMI indicates obesity, which increases risk for several chronic conditions.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Low")
            elif bmi < 18.5:
                medical_advice.append("⚠️ Underweight detected. This may indicate nutritional deficiencies or other health issues.")
                specialist_recommendations.append("**Nutritionist/Dietitian**: For nutritional assessment.")
                integrated_analysis.append("Your BMI indicates you are underweight, which may affect your overall health and immune function.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Low")

        # Check infectious disease markers
        malaria = vital_signs_data.get("Malaria")
        if malaria == "Unknown":
            # Skip Unknown values - don't count them as issues
            pass
        elif malaria == "Positive":
            medical_advice.append("🚨 Malaria detected. This requires prompt medical treatment.")
            specialist_recommendations.append("**Infectious Disease Specialist**: For treatment of malaria.")
            integrated_analysis.append("Your test indicates malaria infection, which requires appropriate antimalarial medication.")
            need_doctor_visit = True
            urgency_level = "High"

        hepatitis_b = vital_signs_data.get("Hepatitis B")
        if hepatitis_b == "Unknown":
            # Skip Unknown values - don't count them as issues
            pass
        elif hepatitis_b == "Positive":
            medical_advice.append("⚠️ Hepatitis B detected. This requires medical evaluation and monitoring.")
            specialist_recommendations.append("**Hepatologist/Gastroenterologist**: For management of hepatitis B.")
            integrated_analysis.append("Your test indicates hepatitis B infection, which requires monitoring of liver function and potential antiviral therapy.")
            need_doctor_visit = True
            urgency_level = max(urgency_level, "Medium")

        # Integrate kidney function data
        if kidney_function_data:
            kidney_health = kidney_function_data.get("overall_health", "").lower()
            kidney_stage = kidney_function_data.get("kidney_stage", "Unknown")

            # Determine severity based on kidney stage
            if kidney_stage and "G5" in kidney_stage:
                medical_advice.append("🚨 Kidney failure (Stage G5) detected. This requires immediate medical attention.")
                specialist_recommendations.append("**Nephrologist**: For urgent evaluation and management of kidney failure.")
                integrated_analysis.append(f"Your kidney function test indicates kidney failure ({kidney_stage}), requiring immediate medical intervention.")
                need_doctor_visit = True
                urgency_level = "High"
            elif kidney_stage and "G4" in kidney_stage:
                medical_advice.append("⚠️ Severe kidney disease (Stage G4) detected. This requires prompt medical evaluation.")
                specialist_recommendations.append("**Nephrologist**: For evaluation and management of severe kidney disease.")
                integrated_analysis.append(f"Your kidney function test indicates severe kidney disease ({kidney_stage}), requiring prompt nephrology care.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")
            elif kidney_stage and "G3" in kidney_stage:
                medical_advice.append("⚠️ Moderate kidney disease detected. This requires medical evaluation.")
                specialist_recommendations.append("**Nephrologist**: For evaluation and management of kidney function.")
                integrated_analysis.append(f"Your kidney function test indicates moderate kidney disease ({kidney_stage}), requiring nephrology consultation.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")
            elif kidney_stage and "G2" in kidney_stage:
                medical_advice.append("ℹ️ Mild kidney disease detected. This requires monitoring and lifestyle adjustments.")
                integrated_analysis.append(f"Your kidney function test indicates mild kidney disease ({kidney_stage}), requiring regular monitoring.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Low")
            elif "poor" in kidney_health or "concerning" in kidney_health or "severe" in kidney_health:
                medical_advice.append("⚠️ Kidney function concerns detected. This requires medical evaluation.")
                specialist_recommendations.append("**Nephrologist**: For evaluation and management of kidney function.")
                integrated_analysis.append("Your kidney function test indicates potential renal issues that require further investigation.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")

            # Add kidney function recommendations to integrated analysis
            if "recommendations" in kidney_function_data and kidney_function_data["recommendations"]:
                for rec in kidney_function_data["recommendations"]:
                    integrated_analysis.append(f"Kidney function: {rec}")

        # Integrate lipid profile data
        if lipid_profile_data:
            ascvd_risk = lipid_profile_data.get("ascvd_risk", "").lower()
            if "high" in ascvd_risk:
                medical_advice.append("⚠️ High cardiovascular risk detected based on lipid profile.")
                specialist_recommendations.append("**Cardiologist**: For cardiovascular risk management.")
                integrated_analysis.append("Your lipid profile indicates elevated cardiovascular risk that requires medical attention.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Medium")
            elif "moderate" in ascvd_risk:
                medical_advice.append("⚠️ Moderate cardiovascular risk detected based on lipid profile.")
                integrated_analysis.append("Your lipid profile indicates moderate cardiovascular risk that should be monitored.")
                need_doctor_visit = True
                urgency_level = max(urgency_level, "Low")

            # Add lipid profile recommendations to integrated analysis
            if "recommendations" in lipid_profile_data and lipid_profile_data["recommendations"]:
                for rec in lipid_profile_data["recommendations"]:
                    integrated_analysis.append(f"Lipid profile: {rec}")

        # Integrate health score data
        if health_score_data:
            score = health_score_data.get("Total Score", 0)
            health_status = health_score_data.get("Health Status", "Unknown")

            # Skip "Unknown" health status
            if health_status != "Unknown":
                if score < 50 or health_status.lower() == "poor":
                    medical_advice.append("⚠️ Poor overall health score detected. Comprehensive medical evaluation recommended.")
                    specialist_recommendations.append("**Primary Care Physician**: For comprehensive health evaluation.")
                    integrated_analysis.append("Your overall health score indicates multiple areas requiring medical attention.")
                    need_doctor_visit = True
                    urgency_level = max(urgency_level, "Medium")
                elif score < 70 or health_status.lower() == "fair":
                    medical_advice.append("⚠️ Fair overall health score detected. Medical evaluation recommended.")
                    integrated_analysis.append("Your overall health score indicates areas that could benefit from medical attention.")
                    need_doctor_visit = True
                    urgency_level = max(urgency_level, "Low")

            # Add improvement tips to integrated analysis
            if "Improvement Tips" in health_score_data and health_score_data["Improvement Tips"]:
                tips = health_score_data["Improvement Tips"]
                if isinstance(tips, list):
                    for tip in tips:
                        if tip and tip.strip():
                            integrated_analysis.append(f"Health score: {tip.strip()}")
                elif tips:  # Check if tips is not empty
                    integrated_analysis.append(f"Health score: {tips}")

        # If no specific issues found but we have health data
        if not medical_advice and (vital_signs_data or health_score_data or kidney_function_data or lipid_profile_data):
            medical_advice.append("✅ No immediate health concerns detected based on available data.")
            integrated_analysis.append("Your health metrics are within normal ranges based on the data provided.")

        # If no health data available
        if not vital_signs_data and not health_score_data and not kidney_function_data and not lipid_profile_data:
            medical_advice.append("⚠️ Insufficient health data available for comprehensive consultation.")
            integrated_analysis.append("Please provide health data for a more accurate health consultation.")

        # Compile final result
        consultation_result["medical_advice"] = medical_advice
        consultation_result["specialist_recommendations"] = list(set(specialist_recommendations))  # Remove duplicates
        consultation_result["doctor_visit_recommended"] = need_doctor_visit
        consultation_result["urgency_level"] = urgency_level
        consultation_result["integrated_analysis"] = integrated_analysis

        # Add appointment booking link if doctor visit is recommended or there are appointments/tests recommended
        if (need_doctor_visit or consultation_result.get("appointment_recommendations") or
            consultation_result.get("recommended_tests") or specialist_recommendations):
            consultation_result["appointment_booking_link"] = "https://app.turbomedics.com/patient/appointment"
            consultation_result["booking_message"] = "Click the link above to book an appointment with healthcare professionals for further evaluation and care. Once you get to the TurboMedics appointment page, click on the 'Book Now' button. You'll see two appointment types: Online Appointment (virtual consultation via video call) and Physical Appointment (in-person visit at a healthcare facility)."

        return json.dumps(consultation_result, indent=4)

    except Exception as e:
        return json.dumps({
            "error": f"Error during health consultation: {str(e)}",
            "medical_advice": ["⚠️ Unable to process health data. Please consult a healthcare professional."],
            "doctor_visit_recommended": True
        }, indent=4)

automated_health_consultation_tool = Tool(
    name="AutomatedHealthConsultation",
    func=automated_health_consultation,
    description="Acts as a virtual doctor, analyzing comprehensive health data and providing integrated medical advice, specialist recommendations, appointment scheduling, and proactive care coordination."
)
