#!/usr/bin/env python3
"""
Simulate the health consultation endpoint logic to verify it works
"""

import json
import sys
import os
from datetime import datetime

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simulate_health_consultation_endpoint():
    """Simulate the health consultation endpoint logic"""
    
    print("🧪 Simulating Health Consultation Endpoint Logic...")
    print("=" * 60)
    
    try:
        # Import the function
        from tools.tools_health_consult import automated_health_consultation
        
        # Simulate user health data storage (like in agent_server.py)
        user_health_data = {}
        chat_histories = {}
        
        # Test user ID
        test_user_id = "test_user_123"
        user_key = test_user_id  # In real server, this would be converted
        
        print("1. Setting up test user health data...")
        print("-" * 40)
        
        # Add test health data (simulating what would be stored from previous assessments)
        user_health_data[user_key] = {
            "vital_signs": {
                "data": {
                    "Blood Pressure (Systolic)": 160,  # High
                    "Blood Pressure (Diastolic)": 95,   # High
                    "Glucose": 140,                      # Elevated
                    "SpO2": 92,                         # Low
                    "ECG (Heart Rate)": 110,            # Elevated
                    "Weight (BMI)": 32,                 # Obesity
                    "Temperature": 98.6,
                    "Malaria": "Negative",
                    "Widal Test": "Negative",
                    "Hepatitis B": "Negative",
                    "Voluntary Serology": "Negative"
                },
                "result": {
                    "analysis": "Multiple vital signs outside normal ranges",
                    "alerts": "High blood pressure and elevated glucose detected"
                },
                "timestamp": datetime.now().isoformat()
            },
            "health_score": {
                "result": {
                    "Total Score": 45,  # Poor health score
                    "Health Status": "Poor",
                    "Vitals Needing Improvement": ["Blood Pressure", "Glucose", "Weight"],
                    "Improvement Tips": ["Monitor blood pressure daily", "Reduce sugar intake", "Increase physical activity"]
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
        # Initialize chat history
        chat_histories[user_key] = []
        
        print("✅ Test user health data set up successfully")
        
        print("\n2. Simulating endpoint logic...")
        print("-" * 40)
        
        # Simulate the endpoint logic from agent_server.py
        
        # Check if user has health data
        if user_key not in user_health_data or not user_health_data[user_key]:
            print("❌ No health data available")
            return False
        
        print("✅ User health data found")
        
        # Process health data (no treat_unknown_as_null for this test)
        processed_health_data = user_health_data[user_key]
        
        # Prepare input for the consultation tool
        consultation_input = {
            "user_id": test_user_id,
            "health_data": processed_health_data
        }
        
        print("✅ Consultation input prepared")
        
        # Run the comprehensive health consultation tool
        consultation_result = json.loads(automated_health_consultation(json.dumps(consultation_input)))
        
        print("✅ Health consultation completed")
        
        # Save consultation result to user health data (simulate server behavior)
        user_health_data[user_key]["health_consultation"] = {
            "result": consultation_result,
            "timestamp": datetime.now().isoformat()
        }
        
        print("✅ Consultation result saved")
        
        # Format the consultation result (simulate server formatting)
        urgency = consultation_result.get("urgency_level", "Low")
        urgency_emoji = "🟢" if urgency == "Low" else "🟡" if urgency == "Medium" else "🔴" if urgency == "High" else "⚠️"
        
        # Start with a friendly greeting
        formatted_result = "Hi there! I've looked through your health information and here's what I found.\n\n"
        
        # Add urgency level with appropriate wording
        if urgency == "Low":
            formatted_result += f"{urgency_emoji} Overall, your health indicators look good! There's no immediate cause for concern based on the data I can see.\n\n"
        elif urgency == "Medium":
            formatted_result += f"{urgency_emoji} I've noticed a few things that might need some attention. Nothing urgent, but worth discussing with your doctor in the next few weeks.\n\n"
        else:
            formatted_result += f"{urgency_emoji} I've found some concerning indicators that should be addressed soon. I'd recommend speaking with a healthcare provider as soon as possible.\n\n"
        
        # Add medical advice
        medical_advice = consultation_result.get("medical_advice", [])
        if medical_advice:
            formatted_result += "Here's my advice based on your health data:\n\n"
            for advice in medical_advice[:3]:  # Limit for demo
                advice_text = advice
                if advice.startswith("- "):
                    advice_text = advice[2:]
                formatted_result += f"{advice_text}\n\n"
        
        # Add specialist recommendations
        specialist_recommendations = consultation_result.get("specialist_recommendations", [])
        if specialist_recommendations:
            if len(specialist_recommendations) == 1:
                formatted_result += f"Based on what I'm seeing, you might benefit from talking to a {specialist_recommendations[0]}.\n\n"
            else:
                formatted_result += "Based on your health data, you might benefit from consulting with these specialists:\n\n"
                for specialist in specialist_recommendations[:3]:  # Limit for demo
                    formatted_result += f"• A {specialist}\n"
                formatted_result += "\n"
        
        # Add appointment booking link if needed
        if (consultation_result.get("doctor_visit_recommended", False) or 
            consultation_result.get("appointment_recommendations") or 
            consultation_result.get("recommended_tests") or 
            specialist_recommendations):
            
            formatted_result += "**Ready to take the next step?**\n\n"
            formatted_result += "I recommend booking an appointment with a healthcare professional for further evaluation and personalized care.\n\n"
            formatted_result += "**[Click here to book your appointment](https://app.turbomedics.com/patient/appointment)**\n\n"
            formatted_result += "Your health data will be available to help provide you with the best possible care.\n\n"
            formatted_result += "Once you get to the TurboMedics appointment page, click on the 'Book Now' button. You'll see two appointment types:\n"
            formatted_result += "• **Online Appointment** - Virtual consultation via video call\n"
            formatted_result += "• **Physical Appointment** - In-person visit at a healthcare facility\n\n"
        
        # Add to chat history
        chat_histories[user_key].append({"role": "user", "content": "I'd like a health consultation based on my data."})
        chat_histories[user_key].append({"role": "assistant", "content": formatted_result})
        
        # Prepare response (simulate server response)
        response = {
            "consultation": formatted_result,
            "doctor_visit_recommended": consultation_result.get("doctor_visit_recommended", False),
            "urgency_level": urgency,
            "appointment_recommendations": consultation_result.get("appointment_recommendations", []),
            "recommended_tests": consultation_result.get("recommended_tests", []),
            "follow_up_schedule": consultation_result.get("follow_up_schedule", {}),
            "care_coordination": consultation_result.get("care_coordination", []),
            "appointment_booking_link": consultation_result.get("appointment_booking_link", "https://app.turbomedics.com/patient/appointment"),
            "booking_message": consultation_result.get("booking_message", "Click the link to book an appointment with healthcare professionals.")
        }
        
        print("✅ Response formatted successfully")
        
        print("\n3. Verifying response components...")
        print("-" * 40)
        
        # Verify key components
        if "consultation" in response:
            consultation_text = response["consultation"]
            print("✅ Consultation text generated")
            
            if "Hi there!" in consultation_text:
                print("✅ Friendly greeting included")
            
            if "TurboMedics" in consultation_text:
                print("✅ TurboMedics appointment information included")
            
            if "Book Now" in consultation_text:
                print("✅ TurboMedics page instructions included")
            
            if "Online Appointment" in consultation_text and "Physical Appointment" in consultation_text:
                print("✅ Appointment type explanations included")
        
        if response.get("appointment_booking_link") == "https://app.turbomedics.com/patient/appointment":
            print("✅ Correct TurboMedics appointment URL")
        
        if response.get("doctor_visit_recommended"):
            print("✅ Doctor visit recommended (as expected for test data)")
        
        if response.get("urgency_level") == "Medium":
            print("✅ Correct urgency level detected")
        
        appointments = response.get("appointment_recommendations", [])
        if len(appointments) > 0:
            print(f"✅ Appointment recommendations: {len(appointments)} specialists")
        
        tests = response.get("recommended_tests", [])
        if len(tests) > 0:
            print(f"✅ Recommended tests: {len(tests)} tests")
        
        print("\n4. Sample consultation response (first 500 characters):")
        print("-" * 50)
        sample_text = response["consultation"][:500] + "..." if len(response["consultation"]) > 500 else response["consultation"]
        print(sample_text)
        print("-" * 50)
        
        print("\n" + "=" * 60)
        print("🎉 Health Consultation Endpoint Simulation Complete!")
        print("✅ The endpoint logic is working correctly!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Error in endpoint simulation: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    simulate_health_consultation_endpoint()
