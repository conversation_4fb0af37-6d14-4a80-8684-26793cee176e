#!/usr/bin/env python3
"""
Simple test to verify health consultation function works
"""

import json
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_health_consultation_function():
    """Test the health consultation function directly"""
    
    print("🧪 Testing Health Consultation Function...")
    print("=" * 60)
    
    try:
        from tools.tools_health_consult import automated_health_consultation
        print("✅ Successfully imported automated_health_consultation function")
    except ImportError as e:
        print(f"❌ Failed to import function: {str(e)}")
        return False
    
    # Test data with health issues that should trigger recommendations
    test_health_data = {
        "user_id": "test_user_123",
        "health_data": {
            "vital_signs": {
                "data": {
                    "Blood Pressure (Systolic)": 160,  # High blood pressure
                    "Blood Pressure (Diastolic)": 95,   # High blood pressure
                    "Glucose": 140,                      # Elevated glucose
                    "SpO2": 92,                         # Low oxygen saturation
                    "ECG (Heart Rate)": 110,            # Elevated heart rate
                    "Weight (BMI)": 32,                 # Obesity
                    "Temperature": 98.6,
                    "Malaria": "Negative",
                    "Widal Test": "Negative",
                    "Hepatitis B": "Negative",
                    "Voluntary Serology": "Negative"
                },
                "result": {
                    "analysis": "Multiple vital signs outside normal ranges",
                    "alerts": "High blood pressure and elevated glucose detected"
                },
                "timestamp": "2024-01-15T10:30:00"
            },
            "health_score": {
                "result": {
                    "Total Score": 45,  # Poor health score
                    "Health Status": "Poor",
                    "Vitals Needing Improvement": ["Blood Pressure", "Glucose", "Weight"],
                    "Improvement Tips": ["Monitor blood pressure daily", "Reduce sugar intake", "Increase physical activity"]
                },
                "timestamp": "2024-01-15T10:30:00"
            }
        }
    }
    
    print("\n1. Testing automated_health_consultation function...")
    print("-" * 40)
    
    try:
        # Call the function
        result_json = automated_health_consultation(json.dumps(test_health_data))
        result = json.loads(result_json)
        
        print("✅ Function executed successfully!")
        
        # Check key components of the response
        if "medical_advice" in result:
            advice_count = len(result["medical_advice"])
            print(f"✅ Medical advice provided: {advice_count} recommendations")
            
            # Show first few pieces of advice
            for i, advice in enumerate(result["medical_advice"][:3]):
                print(f"   • {advice}")
        
        if "specialist_recommendations" in result:
            specialists = result["specialist_recommendations"]
            print(f"✅ Specialist recommendations: {len(specialists)} specialists")
            for specialist in specialists[:3]:
                print(f"   • {specialist}")
        
        if "doctor_visit_recommended" in result:
            visit_recommended = result["doctor_visit_recommended"]
            print(f"✅ Doctor visit recommended: {visit_recommended}")
        
        if "urgency_level" in result:
            urgency = result["urgency_level"]
            print(f"✅ Urgency level: {urgency}")
        
        if "appointment_recommendations" in result:
            appointments = result["appointment_recommendations"]
            print(f"✅ Appointment recommendations: {len(appointments)} appointments")
            for appointment in appointments[:3]:
                print(f"   • {appointment}")
        
        if "recommended_tests" in result:
            tests = result["recommended_tests"]
            print(f"✅ Recommended tests: {len(tests)} tests")
        
        if "appointment_booking_link" in result:
            booking_link = result["appointment_booking_link"]
            print(f"✅ Appointment booking link: {booking_link}")
            
            if booking_link == "https://app.turbomedics.com/patient/appointment":
                print("✅ Correct TurboMedics URL!")
            else:
                print(f"❌ Wrong booking URL: {booking_link}")
        
        if "booking_message" in result:
            booking_message = result["booking_message"]
            if "TurboMedics" in booking_message and "Book Now" in booking_message:
                print("✅ TurboMedics booking instructions included!")
            else:
                print("⚠️ TurboMedics booking instructions may be incomplete")
        
        print("\n2. Testing with healthy data...")
        print("-" * 40)
        
        # Test with healthy data
        healthy_data = {
            "user_id": "healthy_user_123",
            "health_data": {
                "vital_signs": {
                    "data": {
                        "Blood Pressure (Systolic)": 120,  # Normal
                        "Blood Pressure (Diastolic)": 80,   # Normal
                        "Glucose": 90,                       # Normal
                        "SpO2": 98,                         # Normal
                        "ECG (Heart Rate)": 75,             # Normal
                        "Weight (BMI)": 23,                 # Normal
                        "Temperature": 98.6,
                        "Malaria": "Negative",
                        "Widal Test": "Negative",
                        "Hepatitis B": "Negative",
                        "Voluntary Serology": "Negative"
                    },
                    "result": {
                        "analysis": "All vital signs within normal ranges",
                        "alerts": "No abnormal patterns detected"
                    },
                    "timestamp": "2024-01-15T10:30:00"
                },
                "health_score": {
                    "result": {
                        "Total Score": 85,  # Good health score
                        "Health Status": "Good",
                        "Vitals Needing Improvement": ["None"],
                        "Improvement Tips": ["Continue healthy lifestyle"]
                    },
                    "timestamp": "2024-01-15T10:30:00"
                }
            }
        }
        
        healthy_result_json = automated_health_consultation(json.dumps(healthy_data))
        healthy_result = json.loads(healthy_result_json)
        
        print("✅ Healthy data test completed!")
        
        if "medical_advice" in healthy_result:
            advice = healthy_result["medical_advice"]
            if any("No immediate health concerns" in str(a) for a in advice):
                print("✅ Correctly identified healthy status!")
            else:
                print(f"⚠️ Advice for healthy data: {advice}")
        
        print("\n" + "=" * 60)
        print("🎉 Health Consultation Function Test Complete!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing function: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    test_health_consultation_function()
