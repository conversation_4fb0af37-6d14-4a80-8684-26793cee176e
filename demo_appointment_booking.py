#!/usr/bin/env python3
"""
Demo script showing how appointment booking works in the chat interface
"""

import json
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simulate_chat_interaction():
    """Simulate a chat interaction that leads to appointment booking"""
    
    print("🩺 Health Agent Chat Simulation")
    print("=" * 50)
    print("User: I need a health consultation")
    print()
    
    # Simulate agent response for health consultation request
    agent_response = """I'd be happy to provide a comprehensive health consultation based on your data. This will include an analysis of your health metrics, personalized recommendations, specialist referrals if needed, recommended appointments, tests, follow-up schedules, and proactive care coordination. Would you like me to do that now? Just type 'yes' and I'll get started.

If you'd prefer to book an appointment with a healthcare professional right away, you can **[click here to book an appointment](https://app.turbomedics.com/patient/appointment)**."""
    
    print("Agent:", agent_response)
    print()
    print("-" * 50)
    print("User: yes")
    print()
    
    # Simulate comprehensive health consultation response
    consultation_response = """Hi there! I've looked through your health information and here's what I found.

🟡 I've noticed a few things that might need some attention. Nothing urgent, but worth discussing with your doctor in the next few weeks.

Here's my advice based on your health data:

⚠️ Hypertension detected. This increases risk of heart disease and stroke.

⚠️ Elevated blood sugar detected. This may indicate prediabetes or diabetes.

⚠️ Class I obesity detected. This increases risk of multiple health conditions.

⚠️ Mild hypoxemia detected. This may indicate respiratory problems.

Based on your health data, you might benefit from consulting with these specialists:

• A **Cardiologist**: For evaluation and management of hypertension.
• A **Endocrinologist**: For evaluation of glucose metabolism.
• A **Nutritionist/Dietitian**: For weight management guidance.
• A **Pulmonologist**: For evaluation of respiratory function.

**Recommended Appointments:**

🟡 **Cardiologist**: Elevated blood pressure requiring evaluation
🟡 **Endocrinologist**: Elevated blood glucose requiring evaluation
🟡 **Pulmonologist**: Low oxygen saturation requiring evaluation
🟢 **Primary Care Physician**: Low overall health score requiring comprehensive evaluation

**Recommended Tests and Screenings:**

• 24-hour Blood Pressure Monitoring
• HbA1c Test
• Fasting Blood Glucose
• Pulmonary Function Test
• Comprehensive Health Panel
• Complete Blood Count
• Lipid Profile
• Annual Physical Examination

**Follow-up Schedule:**

• Blood Pressure Check: 2 weeks
• Glucose Check: 1 month
• Oxygen Check: 1 week
• Health Score: 1 month
• Routine Checkup: 1 year

**Proactive Care Coordination:**

• Regular blood pressure monitoring at home
• Regular glucose monitoring
• Oxygen therapy evaluation
• Health coach consultation
• Lifestyle modification program

**Ready to take the next step?**

I recommend booking an appointment with a healthcare professional for further evaluation and personalized care.

**[Click here to book your appointment](https://app.turbomedics.com/patient/appointment)**

Your health data will be available to help provide you with the best possible care."""
    
    print("Agent:", consultation_response)
    print()
    print("-" * 50)
    print("User: I want to book an appointment")
    print()
    
    # Simulate direct appointment booking response
    appointment_response = """I can help you book an appointment with a healthcare professional. **[Click here to book your appointment](https://app.turbomedics.com/patient/appointment)**

This will connect you with qualified healthcare providers who can provide personalized care based on your health needs. Your health data will be available to help provide you with the best possible care."""
    
    print("Agent:", appointment_response)
    print()
    print("=" * 50)
    print("🎉 Demo Complete!")
    print()
    print("Key Features Demonstrated:")
    print("✅ Health consultation with appointment recommendations")
    print("✅ Direct appointment booking requests")
    print("✅ TurboMedics appointment URL integration")
    print("✅ Contextual appointment suggestions based on health data")
    print("✅ Multiple pathways to appointment booking")
    print()
    print("The TurboMedics appointment booking URL is now integrated throughout:")
    print("• Health consultation responses")
    print("• Direct appointment booking requests")
    print("• Error handling scenarios")
    print("• Health assessment tools")
    print()
    print("URL: https://app.turbomedics.com/patient/appointment")

def show_integration_summary():
    """Show a summary of all the integration points"""
    
    print("\n" + "=" * 60)
    print("📋 APPOINTMENT BOOKING INTEGRATION SUMMARY")
    print("=" * 60)
    
    integration_points = [
        {
            "file": "tools/tools_health_consult.py",
            "functions": ["appointment_intelligence_scheduler", "automated_health_consultation"],
            "description": "Added appointment booking links to health consultation tools"
        },
        {
            "file": "agent_server.py", 
            "functions": ["process_agent_query", "health_consultation_endpoint"],
            "description": "Added appointment booking intent detection and response formatting"
        },
        {
            "file": "agent_app.py",
            "functions": ["generate_booking_url"],
            "description": "Updated booking URL function to use TurboMedics URL"
        },
        {
            "file": "MCP/agent_app.py",
            "functions": ["generate_booking_url"],
            "description": "Updated booking URL function to use TurboMedics URL"
        },
        {
            "file": "graph_agent.py",
            "functions": ["handle_appointment"],
            "description": "Updated appointment handling to use TurboMedics URL"
        }
    ]
    
    for i, point in enumerate(integration_points, 1):
        print(f"{i}. {point['file']}")
        print(f"   Functions: {', '.join(point['functions'])}")
        print(f"   Changes: {point['description']}")
        print()
    
    print("🔗 New Appointment Booking URL:")
    print("   https://app.turbomedics.com/patient/appointment")
    print()
    
    print("🎯 Trigger Keywords for Appointment Booking:")
    keywords = [
        "appointment", "schedule", "book appointment", "book", "schedule appointment",
        "checkup", "follow-up", "follow up", "doctor visit", "specialist", 
        "when should i see a doctor", "medical appointment", "see a doctor", 
        "visit doctor", "make appointment", "need appointment", "want appointment",
        "book consultation", "schedule consultation", "reserve appointment"
    ]
    
    for i, keyword in enumerate(keywords):
        if i % 4 == 0:
            print("   ", end="")
        print(f"'{keyword}'", end="")
        if i < len(keywords) - 1:
            print(", ", end="")
        if (i + 1) % 4 == 0:
            print()
    
    if len(keywords) % 4 != 0:
        print()
    
    print("\n✅ Integration Complete!")
    print("Users can now easily book appointments through multiple pathways in the health system.")

if __name__ == "__main__":
    simulate_chat_interaction()
    show_integration_summary()
